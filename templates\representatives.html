
{% extends 'base.html' %}

{% block title %}إدارة المندوبين{% endblock %}

{% block content %}
<!-- Enhanced Representatives Header -->
<div class="dashboard-header mb-5">
    <h1 class="page-header">إدارة المندوبين والموظفين</h1>
    <p class="lead text-center text-muted mb-4">إدارة شاملة لجميع المندوبين والموظفين في النظام</p>
</div>

<!-- Statistics Cards Row -->
<div class="row mb-5">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-gradient hover-lift">
            <div class="card-body text-center">
                <div class="stat-icon mb-3">
                    <i class="bi bi-people-fill display-4 text-primary"></i>
                </div>
                <h3 class="card-title text-gradient">{{ representatives|length }}</h3>
                <p class="card-text text-muted">إجمالي المندوبين</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-gradient hover-lift">
            <div class="card-body text-center">
                <div class="stat-icon mb-3">
                    <i class="bi bi-person-check-fill display-4 text-success"></i>
                </div>
                <h3 class="card-title text-gradient">{{ representatives|length }}</h3>
                <p class="card-text text-muted">المندوبين النشطين</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-gradient hover-lift">
            <div class="card-body text-center">
                <div class="stat-icon mb-3">
                    <i class="bi bi-box-arrow-up display-4 text-info"></i>
                </div>
                <h3 class="card-title text-gradient">0</h3>
                <p class="card-text text-muted">عمليات الصرف اليوم</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-gradient hover-lift">
            <div class="card-body text-center">
                <div class="stat-icon mb-3">
                    <i class="bi bi-award-fill display-4 text-warning"></i>
                </div>
                <h3 class="card-title text-gradient">
                    {% set ranks = representatives|map(attribute='rank')|unique|list %}
                    {{ ranks|length if ranks else 0 }}
                </h3>
                <p class="card-text text-muted">الرتب المختلفة</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Enhanced Representatives Table -->
    <div class="col-lg-8">
        <div class="card hover-lift">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3><i class="bi bi-people-fill"></i> قائمة المندوبين</h3>
                    <div class="header-actions">
                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addRepresentativeModal">
                            <i class="bi bi-person-plus"></i> إضافة مندوب
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if representatives %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="bi bi-hash me-1"></i>الرقم</th>
                                <th><i class="bi bi-person me-1"></i>الاسم</th>
                                <th><i class="bi bi-card-text me-1"></i>رقم الموظف</th>
                                <th><i class="bi bi-award me-1"></i>الرتبة</th>
                                <th><i class="bi bi-activity me-1"></i>الحالة</th>
                                <th><i class="bi bi-gear me-1"></i>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for rep in representatives %}
                            <tr>
                                <td class="fw-bold">#{{ loop.index }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="representative-avatar me-3">
                                            <div class="avatar-circle">
                                                <i class="bi bi-person-fill text-primary"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ rep.name }}</div>
                                            <small class="text-muted">كود: EMP-{{ rep.id }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-info fs-6">{{ rep.employee_id }}</span>
                                </td>
                                <td>
                                    <span class="rank-badge">
                                        {% if rep.rank == 'مدير' %}
                                            <span class="badge badge-danger">
                                                <i class="bi bi-star-fill me-1"></i>{{ rep.rank }}
                                            </span>
                                        {% elif rep.rank == 'مشرف' %}
                                            <span class="badge badge-warning">
                                                <i class="bi bi-shield-fill me-1"></i>{{ rep.rank }}
                                            </span>
                                        {% else %}
                                            <span class="badge badge-primary">
                                                <i class="bi bi-person-badge me-1"></i>{{ rep.rank }}
                                            </span>
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-success">
                                        <i class="bi bi-check-circle me-1"></i>نشط
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{{ url_for('dispatch_page', rep_id=rep.id) }}">
                                                        <i class="bi bi-box-arrow-up me-2 text-success"></i>صرف بضاعة
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ url_for('print_report', rep_id=rep.id) }}" target="_blank">
                                                        <i class="bi bi-file-earmark-text me-2 text-info"></i>عرض التقرير
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#editRepModal{{ rep.id }}">
                                                        <i class="bi bi-pencil-square me-2 text-warning"></i>تعديل البيانات
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" data-bs-toggle="modal" data-bs-target="#deleteRepModal{{ rep.id }}">
                                                        <i class="bi bi-trash me-2"></i>حذف المندوب
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>


                            <!-- Enhanced Edit Representative Modal -->
                            <div class="modal fade" id="editRepModal{{ rep.id }}" tabindex="-1" aria-labelledby="editRepModalLabel{{ rep.id }}" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-primary">
                                            <h5 class="modal-title" id="editRepModalLabel{{ rep.id }}">
                                                <i class="bi bi-person-gear"></i>
                                                تعديل بيانات المندوب: {{ rep.name }}
                                            </h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                                        </div>
                                        <form action="{{ url_for('edit_representative', id=rep.id) }}" method="post" class="needs-validation" novalidate>
                                            <div class="modal-body">
                                                <div class="text-center mb-4">
                                                    <div class="representative-avatar-large mb-3">
                                                        <i class="bi bi-person-circle display-4 text-primary"></i>
                                                    </div>
                                                    <h6 class="text-muted">تحديث بيانات المندوب</h6>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label required">
                                                        <i class="bi bi-person me-1"></i>
                                                        الاسم الكامل
                                                    </label>
                                                    <input type="text" class="form-control focus-ring" name="name"
                                                           value="{{ rep.name }}" required
                                                           placeholder="أدخل الاسم الكامل">
                                                    <div class="invalid-feedback">
                                                        يرجى إدخال اسم المندوب
                                                    </div>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label required">
                                                        <i class="bi bi-card-text me-1"></i>
                                                        رقم الموظف
                                                    </label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">EMP-</span>
                                                        <input type="text" class="form-control focus-ring" name="employee_id"
                                                               value="{{ rep.employee_id }}" required
                                                               placeholder="رقم الموظف">
                                                    </div>
                                                    <div class="form-text">
                                                        <i class="bi bi-info-circle me-1"></i>
                                                        رقم فريد لكل موظف
                                                    </div>
                                                    <div class="invalid-feedback">
                                                        يرجى إدخال رقم الموظف
                                                    </div>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label required">
                                                        <i class="bi bi-award me-1"></i>
                                                        الرتبة أو المنصب
                                                    </label>
                                                    <select class="form-select focus-ring" name="rank" required>
                                                        <option value="">اختر الرتبة</option>
                                                        <option value="مندوب" {% if rep.rank == 'مندوب' %}selected{% endif %}>مندوب</option>
                                                        <option value="مندوب أول" {% if rep.rank == 'مندوب أول' %}selected{% endif %}>مندوب أول</option>
                                                        <option value="مشرف" {% if rep.rank == 'مشرف' %}selected{% endif %}>مشرف</option>
                                                        <option value="مدير" {% if rep.rank == 'مدير' %}selected{% endif %}>مدير</option>
                                                        <option value="أخرى" {% if rep.rank not in ['مندوب', 'مندوب أول', 'مشرف', 'مدير'] %}selected{% endif %}>أخرى</option>
                                                    </select>
                                                    <div class="invalid-feedback">
                                                        يرجى اختيار الرتبة
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                    <i class="bi bi-x-lg me-1"></i>إلغاء
                                                </button>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="bi bi-check-lg me-1"></i>حفظ التغييرات
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Delete Representative Modal -->
                            <div class="modal fade" id="deleteRepModal{{ rep.id }}" tabindex="-1" aria-labelledby="deleteRepModalLabel{{ rep.id }}" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger">
                                            <h5 class="modal-title" id="deleteRepModalLabel{{ rep.id }}">
                                                <i class="bi bi-exclamation-triangle-fill"></i>
                                                تأكيد حذف المندوب
                                            </h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                                        </div>
                                        <form action="{{ url_for('delete_representative', id=rep.id) }}" method="post">
                                            <div class="modal-body">
                                                <div class="text-center mb-4">
                                                    <i class="bi bi-person-x-fill display-1 text-danger mb-3"></i>
                                                    <h4 class="text-danger">تحذير!</h4>
                                                </div>

                                                <div class="alert alert-warning" role="alert">
                                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                                    <strong>هذا الإجراء لا يمكن التراجع عنه!</strong>
                                                </div>

                                                <div class="representative-info bg-light p-3 rounded mb-3">
                                                    <h6 class="mb-2">
                                                        <i class="bi bi-person-fill me-2 text-primary"></i>
                                                        بيانات المندوب المراد حذفه:
                                                    </h6>
                                                    <ul class="list-unstyled mb-0">
                                                        <li><strong>الاسم:</strong> {{ rep.name }}</li>
                                                        <li><strong>رقم الموظف:</strong> {{ rep.employee_id }}</li>
                                                        <li><strong>الرتبة:</strong> {{ rep.rank }}</li>
                                                        <li><strong>الكود:</strong> EMP-{{ rep.id }}</li>
                                                    </ul>
                                                </div>

                                                <div class="alert alert-info" role="alert">
                                                    <i class="bi bi-info-circle-fill me-2"></i>
                                                    <small>
                                                        <strong>ملاحظة:</strong> سيتم حذف جميع سجلات الصرف والعمليات المتعلقة بهذا المندوب نهائياً.
                                                    </small>
                                                </div>

                                                <p class="text-center fw-bold text-danger mb-0">
                                                    هل أنت متأكد من رغبتك في المتابعة؟
                                                </p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                    <i class="bi bi-x-lg me-1"></i>إلغاء
                                                </button>
                                                <button type="submit" class="btn btn-danger">
                                                    <i class="bi bi-person-x-fill me-1"></i>تأكيد الحذف
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="bi bi-people display-1 text-muted mb-3"></i>
                                        <h5 class="text-muted">لا يوجد مندوبين مسجلين</h5>
                                        <p class="text-muted">ابدأ بإضافة مندوبين جدد لإدارة فريق العمل</p>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRepresentativeModal">
                                            <i class="bi bi-person-plus me-1"></i>إضافة مندوب جديد
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <div class="empty-state">
                        <i class="bi bi-people display-1 text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد مندوبين مسجلين</h5>
                        <p class="text-muted">ابدأ بإضافة مندوبين جدد لإدارة فريق العمل بكفاءة</p>
                        <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addRepresentativeModal">
                            <i class="bi bi-person-plus me-2"></i>إضافة أول مندوب
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Enhanced Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions Card -->
        <div class="card hover-lift mb-4">
            <div class="card-header">
                <h4><i class="bi bi-lightning-charge"></i> الإجراءات السريعة</h4>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addRepresentativeModal">
                        <i class="bi bi-person-plus me-2"></i>إضافة مندوب جديد
                    </button>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-info">
                        <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                    </a>
                    <a href="{{ url_for('reports') }}" class="btn btn-warning">
                        <i class="bi bi-graph-up-arrow me-2"></i>عرض التقارير
                    </a>
                </div>
            </div>
        </div>

        <!-- Representatives Summary Card -->
        <div class="card hover-lift">
            <div class="card-header">
                <h4><i class="bi bi-bar-chart"></i> ملخص المندوبين</h4>
            </div>
            <div class="card-body">
                {% if representatives %}
                <div class="summary-stats">
                    {% set ranks_count = {} %}
                    {% for rep in representatives %}
                        {% if ranks_count.update({rep.rank: ranks_count.get(rep.rank, 0) + 1}) %}{% endif %}
                    {% endfor %}

                    {% for rank, count in ranks_count.items() %}
                    <div class="stat-item d-flex justify-content-between align-items-center mb-3">
                        <div class="stat-label">
                            <i class="bi bi-award me-2 text-primary"></i>
                            {{ rank }}
                        </div>
                        <div class="stat-value">
                            <span class="badge badge-primary">{{ count }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="bi bi-info-circle display-4 text-muted mb-3"></i>
                    <p class="text-muted">لا توجد بيانات لعرضها</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Representative Modal -->
<div class="modal fade" id="addRepresentativeModal" tabindex="-1" aria-labelledby="addRepresentativeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title" id="addRepresentativeModalLabel">
                    <i class="bi bi-person-plus"></i>
                    إضافة مندوب جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form action="{{ url_for('add_representative') }}" method="post" class="needs-validation" novalidate>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <i class="bi bi-person-plus-fill display-4 text-success mb-3"></i>
                        <h6 class="text-muted">أضف مندوب جديد إلى فريق العمل</h6>
                    </div>

                    <div class="mb-4">
                        <label class="form-label required">
                            <i class="bi bi-person me-1"></i>
                            الاسم الكامل
                        </label>
                        <input type="text" class="form-control focus-ring" name="name" required
                               placeholder="أدخل الاسم الكامل">
                        <div class="invalid-feedback">
                            يرجى إدخال اسم المندوب
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label required">
                            <i class="bi bi-card-text me-1"></i>
                            رقم الموظف
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">EMP-</span>
                            <input type="text" class="form-control focus-ring" name="employee_id" required
                                   placeholder="رقم الموظف">
                        </div>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            رقم فريد لكل موظف (مثال: 001, 002, ...)
                        </div>
                        <div class="invalid-feedback">
                            يرجى إدخال رقم الموظف
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label required">
                            <i class="bi bi-award me-1"></i>
                            الرتبة أو المنصب
                        </label>
                        <select class="form-select focus-ring" name="rank" required>
                            <option value="">اختر الرتبة</option>
                            <option value="مندوب">مندوب</option>
                            <option value="مندوب أول">مندوب أول</option>
                            <option value="مشرف">مشرف</option>
                            <option value="مدير">مدير</option>
                        </select>
                        <div class="invalid-feedback">
                            يرجى اختيار الرتبة
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-lg me-1"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-lg me-1"></i>إضافة المندوب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.representative-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-lighter);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.avatar-circle {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: var(--surface-color);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
}

.representative-avatar-large {
    position: relative;
    display: inline-block;
}

.rank-badge .badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
}

.summary-stats .stat-item {
    padding: 0.75rem;
    background: var(--surface-secondary);
    border-radius: var(--border-radius-md);
    margin-bottom: 0.5rem;
    transition: var(--transition-fast);
}

.summary-stats .stat-item:hover {
    background: var(--primary-lighter);
    transform: translateX(5px);
}

.stat-label {
    font-weight: 500;
    color: var(--text-color);
}

.stat-value .badge {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

.representative-info {
    background: var(--surface-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
}

@media (max-width: 768px) {
    .btn-group .dropdown-menu {
        position: static !important;
        transform: none !important;
        margin-top: 0.5rem;
    }

    .table-responsive {
        font-size: 0.85rem;
    }

    .representative-avatar {
        width: 35px;
        height: 35px;
    }

    .avatar-circle {
        width: 30px;
        height: 30px;
    }
}
</style>

{% endblock %}
