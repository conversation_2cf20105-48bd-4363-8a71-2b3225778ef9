
{% extends 'base.html' %}

{% block title %}إدارة المندوبين{% endblock %}

{% block content %}
<h1 class="page-header">إدارة المندوبين</h1>
<div class="row">
    <!-- Representatives Table -->
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header">
                <h3><i class="bi bi-people me-2"></i> قائمة المندوبين</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>رقم الموظف</th>
                                <th>الرتبة</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for rep in representatives %}
                            <tr>
                                <td>{{ rep.name }}</td>
                                <td>{{ rep.employee_id }}</td>
                                <td>{{ rep.rank }}</td>
                                <td>
                                    <a href="{{ url_for('print_report', rep_id=rep.id) }}" target="_blank" class="btn btn-sm btn-info">عرض وتعديل الصرف</a>
                                    <a href="{{ url_for('dispatch_page', rep_id=rep.id) }}" class="btn btn-sm btn-success">صرف بضاعة</a>
                                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editRepModal{{ rep.id }}">تعديل</button>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteRepModal{{ rep.id }}">حذف</button>
                                </td>
                            </tr>

                            

                            <!-- Edit Representative Modal -->
                            <div class="modal fade" id="editRepModal{{ rep.id }}" tabindex="-1">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-warning text-dark">
                                            <h5 class="modal-title"><i class="bi bi-pencil-square me-2"></i>تعديل مندوب: {{ rep.name }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <form action="{{ url_for('edit_representative', id=rep.id) }}" method="post">
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <label class="form-label">الاسم</label>
                                                    <input type="text" class="form-control" name="name" value="{{ rep.name }}" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">رقم الموظف</label>
                                                    <input type="text" class="form-control" name="employee_id" value="{{ rep.employee_id }}" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">الرتبة</label>
                                                    <input type="text" class="form-control" name="rank" value="{{ rep.rank }}" required>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                <button type="submit" class="btn btn-warning">حفظ التغييرات</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Delete Representative Modal -->
                            <div class="modal" id="deleteRepModal{{ rep.id }}" tabindex="-1">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger text-white">
                                            <h5 class="modal-title"><i class="bi bi-trash-fill me-2"></i>تأكيد الحذف</h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                        </div>
                                        <form action="{{ url_for('delete_representative', id=rep.id) }}" method="post">
                                            <div class="modal-body">
                                                <p>هل أنت متأكد أنك تريد حذف المندوب '{{ rep.name }}'؟</p>
                                                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center">لا يوجد مندوبين مسجلين.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Representative Form -->
    <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h3><i class="bi bi-person-plus me-2"></i> إضافة مندوب جديد</h3>
            </div>
            <div class="card-body">
                <form action="{{ url_for('add_representative') }}" method="post">
                    <div class="mb-3">
                        <label class="form-label">الاسم</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم الموظف</label>
                        <input type="text" class="form-control" name="employee_id" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الرتبة</label>
                        <input type="text" class="form-control" name="rank" required>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">إضافة مندوب</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
