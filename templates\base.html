
<!doctype html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="نظام إدارة المخزون المتطور - إدارة شاملة للمنتجات والمندوبين والتقارير">
    <meta name="keywords" content="إدارة المخزون, نظام إدارة, مخزون, منتجات, تقارير">
    <meta name="author" content="نظام إدارة المخزون">
    <meta name="theme-color" content="#4f46e5">

    <title>{% block title %}{% endblock %} - نظام إدارة المخزون المتطور</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📦</text></svg>">

    <!-- Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">

    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap"></noscript>
</head>
<body>
    {% if session.logged_in %}
    <!-- Enhanced Navigation Bar -->
    <nav class="navbar navbar-expand-lg" id="mainNavbar">
        <div class="container-fluid">
            <a class="navbar-brand hover-scale" href="{{ url_for('dashboard') }}">
                نظام إدارة المخزون
            </a>

            <button class="navbar-toggler focus-ring" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="فتح/إغلاق القائمة">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link focus-ring {% if request.endpoint == 'dashboard' %}active{% endif %}"
                           href="{{ url_for('dashboard') }}" title="لوحة التحكم الرئيسية">
                            <i class="bi bi-speedometer2"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link focus-ring {% if request.endpoint == 'manage_representatives' %}active{% endif %}"
                           href="{{ url_for('manage_representatives') }}" title="إدارة المندوبين والموظفين">
                            <i class="bi bi-people-fill"></i>
                            <span>إدارة المندوبين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link focus-ring {% if request.endpoint == 'reports' %}active{% endif %}"
                           href="{{ url_for('reports') }}" title="التقارير والإحصائيات">
                            <i class="bi bi-graph-up-arrow"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle focus-ring" href="#" role="button" data-bs-toggle="dropdown"
                           aria-expanded="false" title="إعدادات المستخدم">
                            <i class="bi bi-person-circle"></i>
                            <span>{{ session.username }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#profileModal">
                                    <i class="bi bi-person-gear me-2"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="{{ url_for('logout') }}">
                                    <i class="bi bi-box-arrow-right me-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Main Content Area -->
    <main class="container mt-custom" role="main">
        <!-- Flash Messages with Enhanced Styling -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages" id="flashMessages">
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert" aria-live="polite">
                        {% if category == 'success' %}
                            <i class="bi bi-check-circle-fill"></i>
                        {% elif category == 'danger' %}
                            <i class="bi bi-exclamation-triangle-fill"></i>
                        {% elif category == 'warning' %}
                            <i class="bi bi-exclamation-circle-fill"></i>
                        {% elif category == 'info' %}
                            <i class="bi bi-info-circle-fill"></i>
                        {% endif %}
                        <span>{{ message }}</span>
                        <button type="button" class="btn-close focus-ring" data-bs-dismiss="alert" aria-label="إغلاق التنبيه"></button>
                    </div>
                {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Page Content -->
        <div class="content-wrapper">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Profile Modal -->
    <div class="modal fade" id="profileModal" tabindex="-1" aria-labelledby="profileModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title" id="profileModalLabel">
                        <i class="bi bi-person-gear"></i>
                        الملف الشخصي
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <div class="profile-avatar">
                            <i class="bi bi-person-circle display-1 text-primary"></i>
                        </div>
                        <h4 class="mt-3">{{ session.username }}</h4>
                        <p class="text-muted">مدير النظام</p>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="bi bi-calendar-check text-success fs-4"></i>
                                <div class="mt-2">
                                    <small class="text-muted">آخر دخول</small>
                                    <div class="fw-bold">اليوم</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="bi bi-shield-check text-primary fs-4"></i>
                                <div class="mt-2">
                                    <small class="text-muted">الصلاحية</small>
                                    <div class="fw-bold">مدير</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3 text-primary fw-bold">جاري التحميل...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
            crossorigin="anonymous"></script>

    <!-- Custom JavaScript -->
    <script>
        // Enhanced navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('mainNavbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Auto-hide flash messages
        setTimeout(function() {
            const flashMessages = document.getElementById('flashMessages');
            if (flashMessages) {
                const alerts = flashMessages.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    setTimeout(function() {
                        bsAlert.close();
                    }, 5000);
                });
            }
        }, 1000);

        // Loading overlay functions
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        // Add loading to form submissions
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(function(form) {
                form.addEventListener('submit', function() {
                    showLoading();
                });
            });
        });

        // Enhanced tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>

    <style>
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            flex-direction: column;
        }

        .content-wrapper {
            animation: fadeInUp 0.6s ease-out;
        }

        .profile-avatar {
            position: relative;
            display: inline-block;
        }

        .profile-avatar::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 20px;
            height: 20px;
            background: #10b981;
            border: 3px solid white;
            border-radius: 50%;
        }
    </style>
</body>
</html>
