
<!doctype html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>تقرير المخزون الحالي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            body { -webkit-print-color-adjust: exact; }
            .no-print { display: none; }
        }
        body {
            background-color: #fff;
        }
        .container {
            width: 80%;
            margin: auto;
            padding-top: 30px;
        }
        .report-header {
            text-align: center;
            margin-bottom: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="report-header">
            <h2>تقرير المخزون الحالي</h2>
            <p>تاريخ الطباعة: {{ print_date }}</p>
        </div>

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>#</th>
                    <th>اسم المنتج</th>
                    <th>الكمية الحالية</th>
                </tr>
            </thead>
            <tbody>
                {% for product in products %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ product.name }}</td>
                    <td>{{ product.quantity }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="text-center mt-5 no-print">
            <button onclick="window.print();" class="btn btn-primary">طباعة التقرير</button>
            <a href="{{ url_for('reports') }}" class="btn btn-secondary">العودة للتقارير</a>
        </div>
    </div>
</body>
</html>
