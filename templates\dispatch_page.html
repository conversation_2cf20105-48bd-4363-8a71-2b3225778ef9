
{% extends 'base.html' %}

{% block title %}صرف بضاعة للمندوب: {{ representative.name }}{% endblock %}

{% block content %}
<!-- Enhanced Dispatch Header -->
<div class="dashboard-header mb-5">
    <h1 class="page-header">صرف بضاعة للمندوب</h1>
    <p class="lead text-center text-muted mb-4">نموذج صرف البضائع والمنتجات للمندوبين</p>
</div>

<div class="row">
    <!-- Representative Info Card -->
    <div class="col-lg-4 mb-4">
        <div class="card hover-lift">
            <div class="card-header bg-info">
                <h4 class="mb-0">
                    <i class="bi bi-person-badge"></i>
                    بيانات المندوب
                </h4>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="representative-avatar-large mb-3">
                        <i class="bi bi-person-circle display-3 text-info"></i>
                    </div>
                </div>

                <div class="representative-details">
                    <div class="detail-item mb-3">
                        <div class="detail-label">
                            <i class="bi bi-person me-2 text-primary"></i>
                            <strong>الاسم الكامل:</strong>
                        </div>
                        <div class="detail-value">{{ representative.name }}</div>
                    </div>

                    <div class="detail-item mb-3">
                        <div class="detail-label">
                            <i class="bi bi-card-text me-2 text-success"></i>
                            <strong>رقم الموظف:</strong>
                        </div>
                        <div class="detail-value">
                            <span class="badge badge-success">{{ representative.employee_id }}</span>
                        </div>
                    </div>

                    <div class="detail-item mb-3">
                        <div class="detail-label">
                            <i class="bi bi-award me-2 text-warning"></i>
                            <strong>الرتبة:</strong>
                        </div>
                        <div class="detail-value">
                            {% if representative.rank == 'مدير' %}
                                <span class="badge badge-danger">
                                    <i class="bi bi-star-fill me-1"></i>{{ representative.rank }}
                                </span>
                            {% elif representative.rank == 'مشرف' %}
                                <span class="badge badge-warning">
                                    <i class="bi bi-shield-fill me-1"></i>{{ representative.rank }}
                                </span>
                            {% else %}
                                <span class="badge badge-primary">
                                    <i class="bi bi-person-badge me-1"></i>{{ representative.rank }}
                                </span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">
                            <i class="bi bi-hash me-2 text-info"></i>
                            <strong>كود المندوب:</strong>
                        </div>
                        <div class="detail-value">
                            <span class="badge badge-info">EMP-{{ representative.id }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dispatch Form Card -->
    <div class="col-lg-8">
        <div class="card hover-lift">
            <div class="card-header bg-success">
                <h4 class="mb-0">
                    <i class="bi bi-box-arrow-up"></i>
                    نموذج صرف البضاعة
                </h4>
            </div>
            <div class="card-body">
                {% if products %}
                <form action="{{ url_for('dispatch_product') }}" method="post" class="needs-validation" novalidate id="dispatchForm">
                    <input type="hidden" name="representative_id" value="{{ representative.id }}">

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <label for="product_id" class="form-label required">
                                <i class="bi bi-box me-1"></i>
                                اختيار المنتج
                            </label>
                            <select id="product_id" class="form-select focus-ring" name="product_id" required>
                                <option value="" disabled selected>-- اختر المنتج المراد صرفه --</option>
                                {% for product in products %}
                                <option value="{{ product.id }}" data-quantity="{{ product.quantity }}">
                                    {{ product.name }} (المتوفر: {{ product.quantity }} قطعة)
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                اختر المنتج من القائمة المتاحة
                            </div>
                            <div class="invalid-feedback">
                                يرجى اختيار المنتج
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <label for="dispatch_quantity" class="form-label required">
                                <i class="bi bi-123 me-1"></i>
                                الكمية المصروفة
                            </label>
                            <div class="input-group">
                                <input id="dispatch_quantity" type="number" class="form-control focus-ring"
                                       name="dispatch_quantity" required min="1" placeholder="0">
                                <span class="input-group-text">قطعة</span>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-exclamation-triangle me-1"></i>
                                <span id="available-quantity">اختر المنتج أولاً لمعرفة الكمية المتاحة</span>
                            </div>
                            <div class="invalid-feedback">
                                يرجى إدخال كمية صحيحة
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="notes" class="form-label">
                            <i class="bi bi-chat-text me-1"></i>
                            ملاحظات إضافية
                        </label>
                        <textarea id="notes" class="form-control focus-ring" name="notes" rows="4"
                                  placeholder="أضف أي ملاحظات أو تفاصيل إضافية حول عملية الصرف..."></textarea>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            اختياري - يمكنك إضافة ملاحظات حول عملية الصرف
                        </div>
                    </div>

                    <!-- Dispatch Summary -->
                    <div class="dispatch-summary bg-light p-3 rounded mb-4" id="dispatchSummary" style="display: none;">
                        <h6 class="mb-3">
                            <i class="bi bi-clipboard-check me-2 text-success"></i>
                            ملخص عملية الصرف
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">المندوب:</small>
                                <div class="fw-bold">{{ representative.name }}</div>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">المنتج:</small>
                                <div class="fw-bold" id="selectedProduct">-</div>
                            </div>
                            <div class="col-md-6 mt-2">
                                <small class="text-muted">الكمية:</small>
                                <div class="fw-bold" id="selectedQuantity">-</div>
                            </div>
                            <div class="col-md-6 mt-2">
                                <small class="text-muted">التاريخ:</small>
                                <div class="fw-bold" id="currentDate">{{ moment().format('YYYY-MM-DD HH:mm') }}</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ url_for('manage_representatives') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right me-1"></i>العودة للمندوبين
                        </a>
                        <button type="submit" class="btn btn-success btn-lg" id="submitBtn">
                            <i class="bi bi-check-circle me-2"></i>تأكيد الصرف
                        </button>
                    </div>
                </form>
                {% else %}
                <div class="text-center py-5">
                    <div class="empty-state">
                        <i class="bi bi-box display-1 text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد منتجات متاحة للصرف</h5>
                        <p class="text-muted">يجب إضافة منتجات إلى المخزون أولاً</p>
                        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>إضافة منتجات
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.representative-details .detail-item {
    padding: 0.75rem;
    background: var(--surface-secondary);
    border-radius: var(--border-radius-md);
    border-right: 4px solid var(--primary-color);
}

.detail-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.detail-value {
    font-size: 1rem;
    color: var(--text-color);
}

.dispatch-summary {
    border: 2px dashed var(--success-color);
    background: var(--success-light) !important;
}

#dispatchForm {
    position: relative;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.25rem rgba(16, 185, 129, 0.15);
}

@media (max-width: 768px) {
    .representative-details .detail-item {
        margin-bottom: 1rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-lg {
        width: 100%;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const productSelect = document.getElementById('product_id');
    const quantityInput = document.getElementById('dispatch_quantity');
    const availableQuantitySpan = document.getElementById('available-quantity');
    const dispatchSummary = document.getElementById('dispatchSummary');
    const selectedProduct = document.getElementById('selectedProduct');
    const selectedQuantity = document.getElementById('selectedQuantity');
    const form = document.getElementById('dispatchForm');

    // Update available quantity when product is selected
    productSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const availableQty = selectedOption.getAttribute('data-quantity');

        if (availableQty) {
            availableQuantitySpan.innerHTML = `<i class="bi bi-check-circle me-1 text-success"></i>الكمية المتاحة: ${availableQty} قطعة`;
            quantityInput.max = availableQty;
            quantityInput.placeholder = `الحد الأقصى: ${availableQty}`;

            // Update summary
            selectedProduct.textContent = selectedOption.textContent.split(' (المتوفر:')[0];
            updateSummary();
        }
    });

    // Update summary when quantity changes
    quantityInput.addEventListener('input', function() {
        updateSummary();
    });

    function updateSummary() {
        if (productSelect.value && quantityInput.value) {
            selectedQuantity.textContent = quantityInput.value + ' قطعة';
            dispatchSummary.style.display = 'block';
        } else {
            dispatchSummary.style.display = 'none';
        }
    }

    // Form validation
    form.addEventListener('submit', function(e) {
        const selectedOption = productSelect.options[productSelect.selectedIndex];
        const availableQty = parseInt(selectedOption.getAttribute('data-quantity'));
        const requestedQty = parseInt(quantityInput.value);

        if (requestedQty > availableQty) {
            e.preventDefault();
            alert(`الكمية المطلوبة (${requestedQty}) أكبر من الكمية المتاحة (${availableQty})`);
            quantityInput.focus();
            return false;
        }

        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Set current date
    const now = new Date();
    const currentDate = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
    document.getElementById('currentDate').textContent = currentDate;
});
</script>

{% endblock %}
