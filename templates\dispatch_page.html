
{% extends 'base.html' %}

{% block title %}صرف بضاعة للمندوب: {{ representative.name }}{% endblock %}

{% block content %}
<h1 class="page-header"><i class="bi bi-box-arrow-up me-2"></i>صرف بضاعة للمندوب: {{ representative.name }}</h1>

<div class="card shadow-sm">
    <div class="card-header">
        <h3>بيانات المندوب</h3>
    </div>
    <div class="card-body">
        <p><strong>الاسم:</strong> {{ representative.name }}</p>
        <p><strong>رقم الموظف:</strong> {{ representative.employee_id }}</p>
        <p><strong>الرتبة:</strong> {{ representative.rank }}</p>
    </div>
</div>

<div class="card shadow-sm mt-4">
    <div class="card-header">
        <h3>نموذج الصرف</h3>
    </div>
    <div class="card-body">
        <form action="{{ url_for('dispatch_product') }}" method="post">
            <input type="hidden" name="representative_id" value="{{ representative.id }}">
            <div class="mb-3">
                <label for="product_id" class="form-label">المنتج</label>
                <select id="product_id" class="form-select" name="product_id" required>
                    <option value="" disabled selected>-- اختر المنتج --</option>
                    {% for product in products %}
                    <option value="{{ product.id }}">{{ product.name }} (المتوفر: {{ product.quantity }})</option>
                    {% endfor %}
                </select>
            </div>
            <div class="mb-3">
                <label for="dispatch_quantity" class="form-label">الكمية المصروفة</label>
                <input id="dispatch_quantity" type="number" class="form-control" name="dispatch_quantity" required min="1">
            </div>
            <div class="mb-3">
                <label for="notes" class="form-label">ملاحظات</label>
                <textarea id="notes" class="form-control" name="notes" rows="3"></textarea>
            </div>
            <div class="d-flex justify-content-end">
                <a href="{{ url_for('manage_representatives') }}" class="btn btn-secondary me-2">إلغاء</a>
                <button type="submit" class="btn btn-success">تأكيد الصرف</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
