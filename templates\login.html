
{% extends 'base.html' %}

{% block title %}تسجيل الدخول{% endblock %}

{% block content %}
<div class="login-container">
    <div class="card login-card">
        <div class="card-body">
            <!-- Enhanced Login Header -->
            <div class="text-center mb-5">
                <div class="login-logo mb-4">
                    <div class="logo-icon">
                        <i class="bi bi-shield-lock display-1 text-primary"></i>
                    </div>
                </div>
                <h2 class="card-title text-gradient mb-2">نظام إدارة المخزون</h2>
                <p class="text-muted">تسجيل دخول المدير</p>
            </div>

            <!-- Enhanced Login Form -->
            <form method="post" class="needs-validation" novalidate id="loginForm">
                <div class="mb-4">
                    <label for="username" class="form-label required">
                        <i class="bi bi-person me-2"></i>
                        اسم المستخدم
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-person-fill"></i>
                        </span>
                        <input type="text" class="form-control focus-ring" id="username" name="username"
                               required autocomplete="username" placeholder="أدخل اسم المستخدم">
                        <div class="invalid-feedback">
                            يرجى إدخال اسم المستخدم
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="password" class="form-label required">
                        <i class="bi bi-lock me-2"></i>
                        كلمة المرور
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-lock-fill"></i>
                        </span>
                        <input type="password" class="form-control focus-ring" id="password" name="password"
                               required autocomplete="current-password" placeholder="أدخل كلمة المرور">
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="bi bi-eye" id="togglePasswordIcon"></i>
                        </button>
                        <div class="invalid-feedback">
                            يرجى إدخال كلمة المرور
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input focus-ring" type="checkbox" id="rememberMe" name="remember">
                        <label class="form-check-label" for="rememberMe">
                            تذكرني
                        </label>
                    </div>
                </div>

                <div class="d-grid mb-4">
                    <button type="submit" class="btn btn-primary btn-lg" id="loginButton">
                        <span class="button-text">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            تسجيل الدخول
                        </span>
                        <span class="button-loading" style="display: none;">
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            جاري تسجيل الدخول...
                        </span>
                    </button>
                </div>
            </form>

            <!-- Security Notice -->
            <div class="security-notice mt-4">
                <div class="alert alert-info" role="alert">
                    <i class="bi bi-info-circle me-2"></i>
                    <small>
                        <strong>ملاحظة أمنية:</strong>
                        يرجى التأكد من أنك تستخدم جهازاً آمناً وموثوقاً لتسجيل الدخول.
                    </small>
                </div>
            </div>
        </div>

        <!-- Login Footer -->
        <div class="card-footer text-center bg-transparent border-0">
            <small class="text-muted">
                <i class="bi bi-shield-check me-1"></i>
                محمي بأحدث تقنيات الأمان
            </small>
        </div>
    </div>
</div>

<style>
.login-logo {
    position: relative;
}

.logo-icon {
    position: relative;
    display: inline-block;
}

.logo-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(79, 70, 229, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
}

.security-notice .alert {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--border-radius-lg);
}

#loginForm {
    position: relative;
    z-index: 1;
}

.input-group-text {
    background: var(--surface-secondary);
    border-color: var(--border-color);
    color: var(--text-muted);
}

.btn-outline-secondary {
    border-color: var(--border-color);
    color: var(--text-muted);
}

.btn-outline-secondary:hover {
    background: var(--surface-secondary);
    border-color: var(--border-medium);
    color: var(--text-color);
}

@media (max-width: 480px) {
    .login-card {
        margin: 1rem;
        padding: 2rem;
    }

    .logo-icon i {
        font-size: 4rem !important;
    }
}
</style>

<script>
// Enhanced login form functionality
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginButton = document.getElementById('loginButton');
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const togglePasswordIcon = document.getElementById('togglePasswordIcon');

    // Password visibility toggle
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);

        if (type === 'text') {
            togglePasswordIcon.classList.remove('bi-eye');
            togglePasswordIcon.classList.add('bi-eye-slash');
        } else {
            togglePasswordIcon.classList.remove('bi-eye-slash');
            togglePasswordIcon.classList.add('bi-eye');
        }
    });

    // Form submission with loading state
    loginForm.addEventListener('submit', function(e) {
        if (loginForm.checkValidity()) {
            const buttonText = loginButton.querySelector('.button-text');
            const buttonLoading = loginButton.querySelector('.button-loading');

            buttonText.style.display = 'none';
            buttonLoading.style.display = 'inline-block';
            loginButton.disabled = true;
        }
    });

    // Form validation
    loginForm.addEventListener('submit', function(e) {
        if (!loginForm.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        loginForm.classList.add('was-validated');
    });

    // Auto-focus on username field
    document.getElementById('username').focus();
});
</script>

{% endblock %}
