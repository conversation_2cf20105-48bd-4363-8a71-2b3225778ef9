
<!doctype html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>تقرير ملخص الصرف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            body { -webkit-print-color-adjust: exact; }
            .no-print { display: none; }
        }
        body {
            background-color: #fff;
        }
        .container {
            width: 80%;
            margin: auto;
            padding-top: 30px;
        }
        .report-header {
            text-align: center;
            margin-bottom: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="report-header">
            <h2>تقرير ملخص الصرف من {{ start_date }} إلى {{ end_date }}</h2>
            <p>تاريخ الطباعة: {{ print_date }}</p>
        </div>

        {% if logs %}
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المندوب</th>
                        <th>الرتبة</th>
                        <th>اسم المنتج</th>
                        <th>إجمالي الكمية المصروفة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in logs %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ log.representative_name }}</td>
                        <td>{{ log.representative_rank }}</td>
                        <td>{{ log.product_name_at_time }}</td>
                        <td>{{ -log.total_dispatched }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="alert alert-info">لا توجد عمليات صرف مسجلة في هذه الفترة.</div>
        {% endif %}

        <div class="text-center mt-5 no-print">
            <button onclick="window.print();" class="btn btn-primary">طباعة التقرير</button>
            <a href="{{ url_for('reports') }}" class="btn btn-secondary">العودة للتقارير</a>
        </div>
    </div>
</body>
</html>
