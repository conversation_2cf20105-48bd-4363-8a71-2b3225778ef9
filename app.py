
import sqlite3
from flask import Flask, render_template, request, redirect, url_for, session, flash
from werkzeug.security import check_password_hash, generate_password_hash
from functools import wraps
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = os.urandom(24)
DATABASE = 'c:/Users/<USER>/Desktop/mraqba/database.db'

def get_db_connection():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

# --- Decorators ---
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            flash('الرجاء تسجيل الدخول للوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# --- Routes ---
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = get_db_connection()
        admin = conn.execute('SELECT * FROM admin WHERE username = ?', (username,)).fetchone()
        conn.close()
        
        if admin and check_password_hash(admin['password'], password):
            session['logged_in'] = True
            session['username'] = admin['username']
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة.', 'danger')
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('login'))

@app.route('/')
@login_required
def dashboard():
    conn = get_db_connection()
    products = conn.execute('SELECT * FROM products ORDER BY name').fetchall()
    logs = conn.execute('SELECT * FROM activity_log ORDER BY timestamp DESC LIMIT 15').fetchall()
    conn.close()
    return render_template('dashboard.html', products=products, logs=logs)

@app.route('/representatives')
@login_required
def manage_representatives():
    conn = get_db_connection()
    representatives = conn.execute('SELECT * FROM representatives ORDER BY name').fetchall()
    products = conn.execute('SELECT * FROM products WHERE quantity > 0 ORDER BY name').fetchall()
    conn.close()
    return render_template('representatives.html', representatives=representatives, products=products)

@app.route('/representatives/add', methods=['POST'])
@login_required
def add_representative():
    name = request.form['name']
    employee_id = request.form['employee_id']
    rank = request.form['rank']

    conn = get_db_connection()
    try:
        conn.execute('INSERT INTO representatives (name, employee_id, rank) VALUES (?, ?, ?)', (name, employee_id, rank))
        conn.commit()
        flash(f'تمت إضافة المندوب "{name}" بنجاح.', 'success')
    except sqlite3.IntegrityError:
        flash(f'رقم الموظف "{employee_id}" موجود بالفعل.', 'danger')
    finally:
        conn.close()
    
    return redirect(url_for('manage_representatives'))

@app.route('/representatives/edit/<int:id>', methods=['POST'])
@login_required
def edit_representative(id):
    name = request.form['name']
    employee_id = request.form['employee_id']
    rank = request.form['rank']

    conn = get_db_connection()
    try:
        conn.execute('UPDATE representatives SET name = ?, employee_id = ?, rank = ? WHERE id = ?', (name, employee_id, rank, id))
        conn.commit()
        flash(f'تم تحديث بيانات المندوب "{name}" بنجاح.', 'success')
    except sqlite3.IntegrityError:
        flash(f'رقم الموظف "{employee_id}" موجود بالفعل.', 'danger')
    finally:
        conn.close()

    return redirect(url_for('manage_representatives'))

@app.route('/representatives/delete/<int:id>', methods=['POST'])
@login_required
def delete_representative(id):
    conn = get_db_connection()
    rep = conn.execute('SELECT name FROM representatives WHERE id = ?', (id,)).fetchone()
    conn.execute('DELETE FROM representatives WHERE id = ?', (id,))
    conn.commit()
    conn.close()
    flash(f'تم حذف المندوب "{rep['name']}" بنجاح.', 'warning')
    return redirect(url_for('manage_representatives'))

@app.route('/dispatch/<int:rep_id>')
@login_required
def dispatch_page(rep_id):
    conn = get_db_connection()
    representative = conn.execute('SELECT * FROM representatives WHERE id = ?', (rep_id,)).fetchone()
    products = conn.execute('SELECT * FROM products WHERE quantity > 0 ORDER BY name').fetchall()
    conn.close()

    if not representative:
        flash('المندوب غير موجود.', 'danger')
        return redirect(url_for('manage_representatives'))

    return render_template('dispatch_page.html', representative=representative, products=products)

@app.route('/add', methods=['POST'])
@login_required
def add_product():
    name = request.form['name']
    quantity = request.form['quantity']

    conn = get_db_connection()
    conn.execute('INSERT INTO products (name, quantity) VALUES (?, ?)', (name, quantity))
    product_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
    conn.execute(
        'INSERT INTO activity_log (product_id, action, quantity_change, representative_name, product_name_at_time) VALUES (?, ?, ?, ?, ?)',
        (product_id, 'إضافة', quantity, session['username'], name)
    )
    conn.commit()
    conn.close()
    
    flash(f'تمت إضافة المنتج "{name}" بنجاح.', 'success')
    return redirect(url_for('dashboard'))

@app.route('/edit/<int:id>', methods=['POST'])
@login_required
def edit_product(id):
    name = request.form['name']
    quantity = request.form['quantity']

    conn = get_db_connection()
    old_product = conn.execute('SELECT * FROM products WHERE id = ?', (id,)).fetchone()
    quantity_change = int(quantity) - old_product['quantity']
    
    conn.execute('UPDATE products SET name = ?, quantity = ? WHERE id = ?', (name, quantity, id))
    conn.execute(
        'INSERT INTO activity_log (product_id, action, quantity_change, representative_name, product_name_at_time) VALUES (?, ?, ?, ?, ?)',
        (id, 'تعديل', quantity_change, session['username'], name)
    )
    conn.commit()
    conn.close()

    flash(f'تم تحديث المنتج "{name}" بنجاح.', 'success')
    return redirect(url_for('dashboard'))

@app.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete_product(id):
    conn = get_db_connection()
    product = conn.execute('SELECT * FROM products WHERE id = ?', (id,)).fetchone()
    conn.execute('DELETE FROM products WHERE id = ?', (id,))
    conn.execute(
        'INSERT INTO activity_log (action, representative_name, product_name_at_time) VALUES (?, ?, ?)',
        ('حذف', session['username'], product['name'])
    )
    conn.commit()
    conn.close()

    flash(f'تم حذف المنتج "{product['name']}" بنجاح.', 'warning')
    return redirect(url_for('dashboard'))

@app.route('/dispatch', methods=['POST'])
@login_required
def dispatch_product():
    dispatch_quantity = int(request.form['dispatch_quantity'])
    rep_id = request.form['representative_id']
    product_id = request.form['product_id']
    notes = request.form.get('notes', '')

    conn = get_db_connection()
    product = conn.execute('SELECT * FROM products WHERE id = ?', (product_id,)).fetchone()
    representative = conn.execute('SELECT * FROM representatives WHERE id = ?', (rep_id,)).fetchone()

    if product and representative and product['quantity'] >= dispatch_quantity:
        new_quantity = product['quantity'] - dispatch_quantity
        conn.execute('UPDATE products SET quantity = ? WHERE id = ?', (new_quantity, product_id))
        conn.execute(
            'INSERT INTO activity_log (product_id, action, quantity_change, representative_name, representative_rank, product_name_at_time, notes) VALUES (?, ?, ?, ?, ?, ?, ?)',
            (product_id, 'صرف', -dispatch_quantity, representative['name'], representative['rank'], product['name'], notes)
        )
        conn.commit()
        flash(f'تم صرف {dispatch_quantity} من المنتج "{product['name']}" إلى {representative['name']}.', 'success')
    elif not product:
        flash('لم يتم العثور على المنتج.', 'danger')
    elif not representative:
        flash('لم يتم العثور على المندوب.', 'danger')
    else:
        flash(f'الكمية المطلوبة من "{product['name']}" غير متوفرة.', 'danger')
    
    conn.close()
    return redirect(url_for('manage_representatives'))

@app.route('/dispatch/edit/<int:log_id>', methods=['POST'])
@login_required
def edit_dispatch_log(log_id):
    new_quantity = int(request.form['new_quantity'])
    notes = request.form.get('notes', '')
    rep_id = request.form['rep_id']

    conn = get_db_connection()
    log = conn.execute('SELECT * FROM activity_log WHERE id = ?', (log_id,)).fetchone()

    if not log:
        flash('لم يتم العثور على سجل الصرف.', 'danger')
        conn.close()
        return redirect(url_for('manage_representatives'))

    # Revert the old stock change
    old_quantity_change = log['quantity_change']
    product_id = log['product_id']
    conn.execute('UPDATE products SET quantity = quantity - ? WHERE id = ?', (old_quantity_change, product_id))

    # Apply the new stock change
    new_quantity_change = -new_quantity
    conn.execute('UPDATE products SET quantity = quantity + ? WHERE id = ?', (new_quantity_change, product_id))

    # Update the log entry
    conn.execute('UPDATE activity_log SET quantity_change = ?, notes = ? WHERE id = ?', (new_quantity_change, notes, log_id))
    
    conn.commit()
    conn.close()

    flash('تم تحديث عملية الصرف بنجاح.', 'success')
    return redirect(url_for('print_report', rep_id=rep_id))

@app.route('/dispatch/delete/<int:log_id>', methods=['POST'])
@login_required
def delete_dispatch_log(log_id):
    rep_id = request.form['rep_id']
    conn = get_db_connection()
    log = conn.execute('SELECT * FROM activity_log WHERE id = ?', (log_id,)).fetchone()

    if not log:
        flash('لم يتم العثور على سجل الصرف.', 'danger')
        conn.close()
        return redirect(url_for('manage_representatives'))

    # Revert the stock change
    quantity_change = log['quantity_change']
    product_id = log['product_id']
    conn.execute('UPDATE products SET quantity = quantity - ? WHERE id = ?', (quantity_change, product_id))

    # Delete the log entry
    conn.execute('DELETE FROM activity_log WHERE id = ?', (log_id,))
    
    conn.commit()
    conn.close()

    flash('تم حذف عملية الصرف بنجاح.', 'success')
    return redirect(url_for('print_report', rep_id=rep_id))

@app.route('/representatives/<int:rep_id>/print')
@login_required
def print_report(rep_id):
    conn = get_db_connection()
    representative = conn.execute('SELECT * FROM representatives WHERE id = ?', (rep_id,)).fetchone()
    logs = conn.execute("""SELECT * FROM activity_log WHERE action = 'صرف' AND representative_name = ? ORDER BY timestamp DESC""", (representative['name'],)).fetchall()
    conn.close()

    if not representative:
        flash('المندوب غير موجود.', 'danger')
        return redirect(url_for('manage_representatives'))

    print_date = datetime.now().strftime('%Y-%m-%d %H:%M')
    return render_template('print_report.html', representative=representative, logs=logs, print_date=print_date)

@app.route('/reports')
@login_required
def reports():
    conn = get_db_connection()
    products = conn.execute('SELECT * FROM products ORDER BY name').fetchall()
    conn.close()
    return render_template('reports.html', products=products)

@app.route('/reports/inventory')
@login_required
def inventory_report():
    conn = get_db_connection()
    products = conn.execute('SELECT * FROM products ORDER BY name').fetchall()
    conn.close()
    print_date = datetime.now().strftime('%Y-%m-%d %H:%M')
    return render_template('inventory_report.html', products=products, print_date=print_date)

@app.route('/reports/low_stock', methods=['POST'])
@login_required
def low_stock_report():
    threshold = request.form.get('threshold', 0)
    conn = get_db_connection()
    products = conn.execute('SELECT * FROM products WHERE quantity <= ? ORDER BY quantity', (threshold,)).fetchall()
    conn.close()
    print_date = datetime.now().strftime('%Y-%m-%d %H:%M')
    return render_template('low_stock_report.html', products=products, print_date=print_date, threshold=threshold)

@app.route('/reports/product_history', methods=['POST'])
@login_required
def product_history_report():
    product_id = request.form.get('product_id')
    conn = get_db_connection()
    product = conn.execute('SELECT * FROM products WHERE id = ?', (product_id,)).fetchone()
    logs = conn.execute('SELECT * FROM activity_log WHERE product_id = ? ORDER BY timestamp DESC', (product_id,)).fetchall()
    conn.close()
    print_date = datetime.now().strftime('%Y-%m-%d %H:%M')
    return render_template('product_history_report.html', logs=logs, product=product, print_date=print_date)

@app.route('/reports/activity_log', methods=['POST'])
@login_required
def activity_log_report():
    start_date = request.form.get('start_date')
    end_date = request.form.get('end_date')
    conn = get_db_connection()
    logs = conn.execute('SELECT * FROM activity_log WHERE DATE(timestamp) BETWEEN ? AND ? ORDER BY timestamp DESC', (start_date, end_date)).fetchall()
    conn.close()
    print_date = datetime.now().strftime('%Y-%m-%d %H:%M')
    return render_template('activity_log_report.html', logs=logs, print_date=print_date, start_date=start_date, end_date=end_date)

@app.route('/reports/dispatch_summary', methods=['POST'])
@login_required
def dispatch_summary_report():
    start_date = request.form.get('start_date')
    end_date = request.form.get('end_date')
    conn = get_db_connection()
    logs = conn.execute("""SELECT representative_name, representative_rank, product_name_at_time, SUM(quantity_change) as total_dispatched
                       FROM activity_log
                       WHERE action = 'صرف' AND DATE(timestamp) BETWEEN ? AND ?
                       GROUP BY representative_name, representative_rank, product_name_at_time
                       ORDER BY representative_name, product_name_at_time""", (start_date, end_date)).fetchall()
    conn.close()
    print_date = datetime.now().strftime('%Y-%m-%d %H:%M')
    return render_template('dispatch_summary_report.html', logs=logs, print_date=print_date, start_date=start_date, end_date=end_date)

if __name__ == '__main__':
    app.run(debug=True)
