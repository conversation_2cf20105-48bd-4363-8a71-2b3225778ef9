"أنشئ تطبيق ويب بسيط بلغة Python لمراقبة وإدارة المخزون باستخدام قاعدة بيانات SQLite. يجب أن يشمل التطبيق:

    نظام تسجيل دخول آمن يتيح فقط لمدير النظام الوصول.

    لوحة تحكم تعرض:

        قائمة المنتجات في المخزون.

        الكمية المتوفرة لكل منتج.

        إمكانية إضافة منتج جديد.

        تعديل الكمية أو تفاصيل المنتج.

        حذف منتج من المخزون.

اضافة مرتبة و اسم المندوب ومكان التوقيع مستلم البضاعة 

    استخدام واجهة ويب مبنية بـ Flask

    واجهة استخدام (Frontend) استخدم BOOTSTRAP لتصميم واجهة احترافية

    قاعدة بيانات SQLite تحتوي على جدول products و جدول admin لتخزين بيانات مدير النظام.

    حماية كاملة للوحة التحكم بحيث لا يمكن الوصول إليها إلا بعد تسجيل الدخول كمدير.

    سجل نشاط (اختياري) يسجل عمليات التعديل والحذف على المنتجات.