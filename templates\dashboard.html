
{% extends 'base.html' %}

{% block title %}لوحة التحكم{% endblock %}

{% block content %}
<h1 class="page-header">لوحة التحكم</h1>
<div class="row">
    <!-- Products Table -->
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header">
                <h3><i class="bi bi-box-seam me-2"></i> قائمة المنتجات</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>اسم المنتج</th>
                                <th>الكمية</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>{{ product.name }}</td>
                                <td>{{ product.quantity }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editModal{{ product.id }}">تعديل</button>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ product.id }}">حذف</button>
                                </td>
                            </tr>

                            <!-- Edit Modal -->
                            <div class="modal fade" id="editModal{{ product.id }}" tabindex="-1">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-warning text-dark">
                                            <h5 class="modal-title"><i class="bi bi-pencil-square me-2"></i>تعديل المنتج: {{ product.name }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <form action="{{ url_for('edit_product', id=product.id) }}" method="post">
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <label class="form-label">اسم المنتج</label>
                                                    <input type="text" class="form-control" name="name" value="{{ product.name }}" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">الكمية</label>
                                                    <input type="number" class="form-control" name="quantity" value="{{ product.quantity }}" required min="0">
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                <button type="submit" class="btn btn-warning">حفظ التغييرات</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Delete Modal -->
                            <div class="modal" id="deleteModal{{ product.id }}" tabindex="-1">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger text-white">
                                            <h5 class="modal-title"><i class="bi bi-trash-fill me-2"></i>تأكيد الحذف</h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                        </div>
                                        <form action="{{ url_for('delete_product', id=product.id) }}" method="post">
                                            <div class="modal-body">
                                                <p>هل أنت متأكد أنك تريد حذف المنتج '{{ product.name }}'؟</p>
                                                <p class="text-muted">سيتم حذف سجلات الحركة المتعلقة بهذا المنتج أيضاً.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center">لا توجد منتجات في المخزون حالياً.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Product Form & Activity Log -->
    <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h3><i class="bi bi-plus-circle me-2"></i> إضافة منتج جديد</h3>
            </div>
            <div class="card-body">
                <form action="{{ url_for('add_product') }}" method="post">
                    <div class="mb-3">
                        <label class="form-label">اسم المنتج</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الكمية</label>
                        <input type="number" class="form-control" name="quantity" required min="0">
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">إضافة للمخزون</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h4><i class="bi bi-clock-history me-2"></i> سجل النشاطات</h4>
            </div>
            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                <ul class="list-group list-group-flush">
                    {% for log in logs %}
                        <li class="list-group-item">
                            <strong>{{ log.action }}</strong>
                            {% if log.product_name_at_time %}
                                للمنتج '{{ log.product_name_at_time }}'
                            {% endif %}
                            <small class="text-muted d-block">بواسطة: {{ log.representative_name }}</small>
                            {% if log.quantity_change is not none and log.quantity_change != 0 %}
                                <small class="d-block">تغيير الكمية: <span class="fw-bold {% if log.quantity_change > 0 %}text-success{% else %}text-danger{% endif %}">{{ '%+d'|format(log.quantity_change) }}</span></small>
                            {% endif %}
                            <small class="text-muted d-block">{{ log.timestamp }}</small>
                        </li>
                    {% else %}
                        <li class="list-group-item">لا توجد أنشطة مسجلة.</li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
