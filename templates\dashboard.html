
{% extends 'base.html' %}

{% block title %}لوحة التحكم{% endblock %}

{% block content %}
<!-- Enhanced Dashboard Header -->
<div class="dashboard-header mb-5">
    <h1 class="page-header">لوحة التحكم الرئيسية</h1>
    <p class="lead text-center text-muted mb-4">مرحباً بك في نظام إدارة المخزون المتطور</p>
</div>

<!-- Statistics Cards Row -->
<div class="row mb-5">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-gradient hover-lift">
            <div class="card-body text-center">
                <div class="stat-icon mb-3">
                    <i class="bi bi-box-seam display-4 text-primary"></i>
                </div>
                <h3 class="card-title text-gradient">{{ products|length }}</h3>
                <p class="card-text text-muted">إجمالي المنتجات</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-gradient hover-lift">
            <div class="card-body text-center">
                <div class="stat-icon mb-3">
                    <i class="bi bi-people-fill display-4 text-success"></i>
                </div>
                <h3 class="card-title text-gradient">{{ representatives|length if representatives else 0 }}</h3>
                <p class="card-text text-muted">المندوبين النشطين</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-gradient hover-lift">
            <div class="card-body text-center">
                <div class="stat-icon mb-3">
                    <i class="bi bi-graph-up-arrow display-4 text-info"></i>
                </div>
                <h3 class="card-title text-gradient">{{ logs|length if logs else 0 }}</h3>
                <p class="card-text text-muted">العمليات اليوم</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-gradient hover-lift">
            <div class="card-body text-center">
                <div class="stat-icon mb-3">
                    <i class="bi bi-exclamation-triangle-fill display-4 text-warning"></i>
                </div>
                <h3 class="card-title text-gradient">
                    {% set low_stock = products|selectattr('quantity', 'lt', 10)|list %}
                    {{ low_stock|length }}
                </h3>
                <p class="card-text text-muted">منتجات قليلة المخزون</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Enhanced Products Table -->
    <div class="col-lg-8">
        <div class="card hover-lift">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3><i class="bi bi-box-seam"></i> قائمة المنتجات</h3>
                    <div class="header-actions">
                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="bi bi-plus-circle"></i> إضافة منتج
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if products %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="bi bi-hash me-1"></i>الرقم</th>
                                <th><i class="bi bi-box me-1"></i>اسم المنتج</th>
                                <th><i class="bi bi-stack me-1"></i>الكمية</th>
                                <th><i class="bi bi-flag me-1"></i>الحالة</th>
                                <th><i class="bi bi-gear me-1"></i>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr class="{% if product.quantity < 10 %}table-warning{% elif product.quantity == 0 %}table-danger{% endif %}">
                                <td class="fw-bold">#{{ loop.index }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="product-icon me-2">
                                            <i class="bi bi-box-seam text-primary"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ product.name }}</div>
                                            <small class="text-muted">كود: PRD-{{ product.id }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge {% if product.quantity > 50 %}badge-success{% elif product.quantity > 10 %}badge-warning{% else %}badge-danger{% endif %} fs-6">
                                        {{ product.quantity }} قطعة
                                    </span>
                                </td>
                                <td>
                                    {% if product.quantity == 0 %}
                                        <span class="badge badge-danger">
                                            <i class="bi bi-x-circle me-1"></i>نفد المخزون
                                        </span>
                                    {% elif product.quantity < 10 %}
                                        <span class="badge badge-warning">
                                            <i class="bi bi-exclamation-triangle me-1"></i>مخزون قليل
                                        </span>
                                    {% else %}
                                        <span class="badge badge-success">
                                            <i class="bi bi-check-circle me-1"></i>متوفر
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                data-bs-toggle="modal" data-bs-target="#editModal{{ product.id }}"
                                                title="تعديل المنتج">
                                            <i class="bi bi-pencil-square"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                data-bs-toggle="modal" data-bs-target="#deleteModal{{ product.id }}"
                                                title="حذف المنتج">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <!-- Enhanced Edit Modal -->
                            <div class="modal fade" id="editModal{{ product.id }}" tabindex="-1" aria-labelledby="editModalLabel{{ product.id }}" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-primary">
                                            <h5 class="modal-title" id="editModalLabel{{ product.id }}">
                                                <i class="bi bi-pencil-square"></i>
                                                تعديل المنتج: {{ product.name }}
                                            </h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                                        </div>
                                        <form action="{{ url_for('edit_product', id=product.id) }}" method="post" class="needs-validation" novalidate>
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-12 mb-4">
                                                        <div class="text-center">
                                                            <i class="bi bi-box-seam display-4 text-primary mb-3"></i>
                                                            <h6 class="text-muted">تعديل بيانات المنتج</h6>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mb-4">
                                                    <label class="form-label required">
                                                        <i class="bi bi-tag me-1"></i>
                                                        اسم المنتج
                                                    </label>
                                                    <input type="text" class="form-control focus-ring" name="name"
                                                           value="{{ product.name }}" required
                                                           placeholder="أدخل اسم المنتج">
                                                    <div class="invalid-feedback">
                                                        يرجى إدخال اسم المنتج
                                                    </div>
                                                </div>
                                                <div class="mb-4">
                                                    <label class="form-label required">
                                                        <i class="bi bi-123 me-1"></i>
                                                        الكمية
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control focus-ring" name="quantity"
                                                               value="{{ product.quantity }}" required min="0"
                                                               placeholder="0">
                                                        <span class="input-group-text">قطعة</span>
                                                    </div>
                                                    <div class="form-text">
                                                        <i class="bi bi-info-circle me-1"></i>
                                                        الحد الأدنى للتنبيه: 10 قطع
                                                    </div>
                                                    <div class="invalid-feedback">
                                                        يرجى إدخال كمية صحيحة
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                    <i class="bi bi-x-lg me-1"></i>إلغاء
                                                </button>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="bi bi-check-lg me-1"></i>حفظ التغييرات
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Delete Modal -->
                            <div class="modal fade" id="deleteModal{{ product.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ product.id }}" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger">
                                            <h5 class="modal-title" id="deleteModalLabel{{ product.id }}">
                                                <i class="bi bi-exclamation-triangle-fill"></i>
                                                تأكيد حذف المنتج
                                            </h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                                        </div>
                                        <form action="{{ url_for('delete_product', id=product.id) }}" method="post">
                                            <div class="modal-body">
                                                <div class="text-center mb-4">
                                                    <i class="bi bi-trash3-fill display-1 text-danger mb-3"></i>
                                                    <h4 class="text-danger">تحذير!</h4>
                                                </div>

                                                <div class="alert alert-warning" role="alert">
                                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                                    <strong>هذا الإجراء لا يمكن التراجع عنه!</strong>
                                                </div>

                                                <div class="product-info bg-light p-3 rounded mb-3">
                                                    <h6 class="mb-2">
                                                        <i class="bi bi-box-seam me-2 text-primary"></i>
                                                        بيانات المنتج المراد حذفه:
                                                    </h6>
                                                    <ul class="list-unstyled mb-0">
                                                        <li><strong>الاسم:</strong> {{ product.name }}</li>
                                                        <li><strong>الكمية:</strong> {{ product.quantity }} قطعة</li>
                                                        <li><strong>الكود:</strong> PRD-{{ product.id }}</li>
                                                    </ul>
                                                </div>

                                                <div class="alert alert-info" role="alert">
                                                    <i class="bi bi-info-circle-fill me-2"></i>
                                                    <small>
                                                        <strong>ملاحظة:</strong> سيتم حذف جميع سجلات الحركة والعمليات المتعلقة بهذا المنتج نهائياً.
                                                    </small>
                                                </div>

                                                <p class="text-center fw-bold text-danger mb-0">
                                                    هل أنت متأكد من رغبتك في المتابعة؟
                                                </p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                    <i class="bi bi-x-lg me-1"></i>إلغاء
                                                </button>
                                                <button type="submit" class="btn btn-danger">
                                                    <i class="bi bi-trash3-fill me-1"></i>تأكيد الحذف
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="bi bi-box display-1 text-muted mb-3"></i>
                                        <h5 class="text-muted">لا توجد منتجات في المخزون</h5>
                                        <p class="text-muted">ابدأ بإضافة منتجات جديدة لإدارة مخزونك</p>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                                            <i class="bi bi-plus-circle me-1"></i>إضافة منتج جديد
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <div class="empty-state">
                        <i class="bi bi-box display-1 text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد منتجات في المخزون</h5>
                        <p class="text-muted">ابدأ بإضافة منتجات جديدة لإدارة مخزونك بكفاءة</p>
                        <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="bi bi-plus-circle me-2"></i>إضافة أول منتج
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Enhanced Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions Card -->
        <div class="card hover-lift mb-4">
            <div class="card-header">
                <h4><i class="bi bi-lightning-charge"></i> الإجراءات السريعة</h4>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addProductModal">
                        <i class="bi bi-plus-circle me-2"></i>إضافة منتج جديد
                    </button>
                    <a href="{{ url_for('manage_representatives') }}" class="btn btn-info">
                        <i class="bi bi-people me-2"></i>إدارة المندوبين
                    </a>
                    <a href="{{ url_for('reports') }}" class="btn btn-warning">
                        <i class="bi bi-graph-up-arrow me-2"></i>عرض التقارير
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced Activity Log -->
        <div class="card hover-lift">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4><i class="bi bi-clock-history"></i> سجل النشاطات</h4>
                    <span class="badge badge-primary">{{ logs|length if logs else 0 }}</span>
                </div>
            </div>
            <div class="card-body" style="max-height: 450px; overflow-y: auto;">
                {% if logs %}
                <div class="activity-timeline">
                    {% for log in logs %}
                    <div class="activity-item">
                        <div class="activity-icon">
                            {% if 'إضافة' in log.action %}
                                <i class="bi bi-plus-circle text-success"></i>
                            {% elif 'تعديل' in log.action %}
                                <i class="bi bi-pencil-square text-warning"></i>
                            {% elif 'حذف' in log.action %}
                                <i class="bi bi-trash text-danger"></i>
                            {% else %}
                                <i class="bi bi-activity text-info"></i>
                            {% endif %}
                        </div>
                        <div class="activity-content">
                            <div class="activity-header">
                                <strong class="activity-action">{{ log.action }}</strong>
                                {% if log.product_name_at_time %}
                                    <span class="activity-product">للمنتج "{{ log.product_name_at_time }}"</span>
                                {% endif %}
                            </div>
                            <div class="activity-details">
                                <small class="text-muted">
                                    <i class="bi bi-person me-1"></i>
                                    بواسطة: {{ log.representative_name }}
                                </small>
                                {% if log.quantity_change is not none and log.quantity_change != 0 %}
                                    <small class="d-block">
                                        <i class="bi bi-arrow-up-down me-1"></i>
                                        تغيير الكمية:
                                        <span class="badge {% if log.quantity_change > 0 %}badge-success{% else %}badge-danger{% endif %}">
                                            {{ '%+d'|format(log.quantity_change) }}
                                        </span>
                                    </small>
                                {% endif %}
                                <small class="text-muted d-block">
                                    <i class="bi bi-clock me-1"></i>
                                    {{ log.timestamp }}
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-clock-history display-4 text-muted mb-3"></i>
                    <h6 class="text-muted">لا توجد أنشطة مسجلة</h6>
                    <p class="text-muted small">ستظهر هنا جميع العمليات والتغييرات</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title" id="addProductModalLabel">
                    <i class="bi bi-plus-circle"></i>
                    إضافة منتج جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form action="{{ url_for('add_product') }}" method="post" class="needs-validation" novalidate>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <i class="bi bi-box-seam display-4 text-success mb-3"></i>
                        <h6 class="text-muted">أضف منتج جديد إلى المخزون</h6>
                    </div>

                    <div class="mb-4">
                        <label class="form-label required">
                            <i class="bi bi-tag me-1"></i>
                            اسم المنتج
                        </label>
                        <input type="text" class="form-control focus-ring" name="name" required
                               placeholder="أدخل اسم المنتج">
                        <div class="invalid-feedback">
                            يرجى إدخال اسم المنتج
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label required">
                            <i class="bi bi-123 me-1"></i>
                            الكمية الأولية
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control focus-ring" name="quantity" required min="0"
                                   placeholder="0" value="1">
                            <span class="input-group-text">قطعة</span>
                        </div>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            يمكنك تعديل الكمية لاحقاً من خلال صفحة إدارة المنتجات
                        </div>
                        <div class="invalid-feedback">
                            يرجى إدخال كمية صحيحة
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-lg me-1"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-lg me-1"></i>إضافة المنتج
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.activity-timeline {
    position: relative;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    position: relative;
}

.activity-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 15px;
    top: 30px;
    bottom: -24px;
    width: 2px;
    background: var(--border-light);
}

.activity-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--surface-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    box-shadow: var(--shadow-sm);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    padding-right: 10px;
}

.activity-header {
    margin-bottom: 5px;
}

.activity-action {
    color: var(--text-color);
    font-size: 0.9rem;
}

.activity-product {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.85rem;
}

.activity-details small {
    display: block;
    margin-bottom: 2px;
    font-size: 0.8rem;
}

.empty-state {
    padding: 2rem;
}

.stat-icon {
    transition: var(--transition-normal);
}

.card:hover .stat-icon {
    transform: scale(1.1);
}
</style>

{% endblock %}
