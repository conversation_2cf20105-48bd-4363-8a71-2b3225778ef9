
/* static/style.css */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

:root {
    /* Enhanced Color Palette */
    --primary-color: #4f46e5;
    --primary-dark: #3730a3;
    --primary-light: #a5b4fc;
    --primary-lighter: #e0e7ff;
    --secondary-color: #7c3aed;
    --secondary-dark: #5b21b6;
    --accent-color: #06b6d4;
    --accent-dark: #0891b2;

    --success-color: #10b981;
    --success-light: #d1fae5;
    --danger-color: #ef4444;
    --danger-light: #fee2e2;
    --warning-color: #f59e0b;
    --warning-light: #fef3c7;
    --info-color: #3b82f6;
    --info-light: #dbeafe;

    /* Enhanced Gradients */
    --background-gradient: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #06b6d4 100%);
    --primary-gradient: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    --secondary-gradient: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
    --accent-gradient: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    --glass-gradient: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

    /* Surface Colors */
    --surface-color: #ffffff;
    --surface-secondary: #f8fafc;
    --surface-tertiary: #f1f5f9;
    --surface-glass: rgba(255, 255, 255, 0.95);
    --surface-glass-dark: rgba(255, 255, 255, 0.85);

    /* Text Colors */
    --text-color: #1e293b;
    --text-light: #475569;
    --text-muted: #64748b;
    --text-disabled: #94a3b8;
    --text-inverse: #ffffff;

    /* Border Colors */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-medium: #cbd5e1;
    --border-dark: #94a3b8;

    /* Enhanced Shadows */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-glow: 0 0 20px rgba(79, 70, 229, 0.3);

    /* Border Radius */
    --border-radius-xs: 4px;
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --border-radius-2xl: 24px;
    --border-radius-full: 9999px;

    /* Transitions */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Cairo', 'Tajawal', 'Amiri', sans-serif;
    background: var(--background-gradient);
    background-attachment: fixed;
    color: var(--text-color);
    direction: rtl;
    line-height: 1.7;
    font-weight: 400;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
    font-feature-settings: "kern" 1, "liga" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Background Pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>') repeat;
    pointer-events: none;
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', 'Amiri', serif;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}

h1 { font-size: 2.5rem; font-weight: 800; }
h2 { font-size: 2rem; font-weight: 700; }
h3 { font-size: 1.75rem; font-weight: 600; }
h4 { font-size: 1.5rem; font-weight: 600; }
h5 { font-size: 1.25rem; font-weight: 500; }
h6 { font-size: 1.125rem; font-weight: 500; }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Enhanced Navigation */
.navbar {
    background: var(--surface-glass) !important;
    backdrop-filter: blur(25px) saturate(180%);
    -webkit-backdrop-filter: blur(25px) saturate(180%);
    box-shadow: var(--shadow-lg);
    border-bottom: 1px solid var(--border-light);
    padding: var(--spacing-lg) var(--spacing-xl);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition-normal);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

.navbar.scrolled {
    background: var(--surface-glass-dark) !important;
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-md) var(--spacing-xl);
}

.navbar-brand {
    font-family: 'Cairo', 'Amiri', serif;
    font-weight: 900;
    font-size: 1.75rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: var(--transition-bounce);
}

.navbar-brand::before {
    content: '📦';
    font-size: 1.5rem;
    margin-left: var(--spacing-sm);
    filter: drop-shadow(0 2px 4px rgba(79, 70, 229, 0.3));
}

.navbar-brand::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--primary-gradient);
    transition: var(--transition-bounce);
    border-radius: var(--border-radius-full);
}

.navbar-brand:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 4px 8px rgba(79, 70, 229, 0.2));
}

.navbar-brand:hover::after {
    width: 100%;
}

.navbar-toggler {
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

.nav-link {
    font-weight: 600;
    color: var(--text-color) !important;
    transition: var(--transition-bounce);
    padding: var(--spacing-md) var(--spacing-lg) !important;
    border-radius: var(--border-radius-lg);
    position: relative;
    overflow: hidden;
    margin: 0 var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.nav-link i {
    font-size: 1.1rem;
    transition: var(--transition-fast);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--glass-gradient);
    transition: var(--transition-normal);
    z-index: -1;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: var(--primary-gradient);
    transition: var(--transition-bounce);
    border-radius: var(--border-radius-full);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 80%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color) !important;
    background: var(--primary-lighter);
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-md);
}

.nav-link:hover i,
.nav-link.active i {
    transform: scale(1.1);
    color: var(--primary-color);
}

/* Enhanced Card Components */
.card {
    border: none;
    border-radius: var(--border-radius-xl);
    background: var(--surface-glass);
    backdrop-filter: blur(25px) saturate(180%);
    -webkit-backdrop-filter: blur(25px) saturate(180%);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-bounce);
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: var(--transition-normal);
}

.card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(79, 70, 229, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition-slow);
    pointer-events: none;
}

.card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(79, 70, 229, 0.3);
}

.card:hover::before {
    opacity: 1;
}

.card:hover::after {
    opacity: 1;
}

.card-header {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
    font-weight: 700;
    padding: var(--spacing-xl);
    border-bottom: 0;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.card-header h3,
.card-header h4,
.card-header h5 {
    margin: 0;
    color: var(--text-inverse);
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-header i {
    font-size: 1.25rem;
    opacity: 0.9;
    transition: var(--transition-fast);
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.card-header:hover::before {
    left: 100%;
}

.card-header:hover i {
    transform: scale(1.1) rotate(5deg);
}

.card-body {
    padding: var(--spacing-xl);
    position: relative;
}

.card-footer {
    background: var(--surface-secondary);
    border-top: 1px solid var(--border-light);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
}

/* Card Variants */
.card.card-glass {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card.card-gradient {
    background: var(--glass-gradient);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.card.card-success .card-header {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.card.card-warning .card-header {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.card.card-danger .card-header {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
}

.card.card-info .card-header {
    background: linear-gradient(135deg, var(--info-color), #2563eb);
}

/* Enhanced Button Components */
.btn {
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    padding: var(--spacing-md) var(--spacing-xl);
    transition: var(--transition-bounce);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
    font-size: 0.95rem;
    cursor: pointer;
    box-shadow: var(--shadow-md);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    font-family: 'Cairo', sans-serif;
    white-space: nowrap;
    user-select: none;
    backdrop-filter: blur(10px);
}

.btn i {
    font-size: 1rem;
    transition: var(--transition-fast);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: var(--transition-normal);
    z-index: 1;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: var(--transition-fast);
    z-index: 0;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover::after {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.btn:hover i {
    transform: scale(1.1);
}

.btn:active {
    transform: translateY(-2px) scale(0.98);
    box-shadow: var(--shadow-lg);
}

.btn:focus {
    outline: none;
    box-shadow: var(--shadow-lg), 0 0 0 3px rgba(79, 70, 229, 0.3);
}

/* Button Variants */
.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    border: 1px solid rgba(79, 70, 229, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
    color: var(--text-inverse);
    box-shadow: var(--shadow-glow);
}

.btn-secondary {
    background: var(--secondary-gradient);
    color: var(--text-inverse);
    border: 1px solid rgba(124, 58, 237, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--secondary-dark), var(--accent-color));
    color: var(--text-inverse);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: var(--text-inverse);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669, #047857);
    color: var(--text-inverse);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    color: var(--text-inverse);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: var(--text-inverse);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
    color: var(--text-inverse);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    color: var(--text-inverse);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #2563eb);
    color: var(--text-inverse);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.btn-info:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: var(--text-inverse);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
}

/* Button Sizes */
.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.85rem;
    border-radius: var(--border-radius-md);
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: 1.125rem;
    border-radius: var(--border-radius-xl);
}

.btn-xl {
    padding: var(--spacing-xl) 3rem;
    font-size: 1.25rem;
    border-radius: var(--border-radius-2xl);
}

/* Button Groups */
.btn-group .btn {
    border-radius: 0;
    margin-left: -1px;
}

.btn-group .btn:first-child {
    border-radius: var(--border-radius-lg) 0 0 var(--border-radius-lg);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
}

/* Enhanced Table Components */
.table {
    background: var(--surface-glass);
    backdrop-filter: blur(20px) saturate(180%);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 0;
}

.table thead th {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    border: none;
    font-weight: 700;
    padding: var(--spacing-lg) var(--spacing-md);
    text-transform: none;
    letter-spacing: 0.025em;
    font-size: 0.95rem;
    position: relative;
    white-space: nowrap;
    font-family: 'Cairo', sans-serif;
}

.table thead th:first-child {
    padding-right: var(--spacing-xl);
}

.table thead th:last-child {
    padding-left: var(--spacing-xl);
}

.table thead th::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: var(--transition-normal);
}

.table thead th:hover::before {
    opacity: 1;
}

.table thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: rgba(255, 255, 255, 0.4);
}

.table tbody tr {
    transition: var(--transition-fast);
    border: none;
    position: relative;
}

.table tbody tr::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass-gradient);
    opacity: 0;
    transition: var(--transition-fast);
    pointer-events: none;
}

.table tbody tr:hover::before {
    opacity: 1;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, var(--primary-lighter), rgba(124, 58, 237, 0.05));
    transform: scale(1.005);
    box-shadow: var(--shadow-sm);
}

.table tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

.table tbody td {
    padding: var(--spacing-lg) var(--spacing-md);
    border: none;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
    font-weight: 500;
    color: var(--text-color);
    position: relative;
    z-index: 1;
}

.table tbody td:first-child {
    padding-right: var(--spacing-xl);
    font-weight: 600;
}

.table tbody td:last-child {
    padding-left: var(--spacing-xl);
}

.table-responsive {
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    background: var(--surface-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

/* Table Variants */
.table-striped tbody tr:nth-of-type(odd) {
    background: rgba(79, 70, 229, 0.02);
}

.table-hover tbody tr:hover {
    background: var(--primary-lighter);
    color: var(--text-color);
}

/* Table Actions */
.table .btn-group {
    display: flex;
    gap: var(--spacing-xs);
}

.table .btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
    min-width: auto;
}

/* Enhanced Form Components */
.form-control, .form-select {
    border-radius: var(--border-radius-lg);
    border: 2px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-lg);
    transition: var(--transition-bounce);
    background: var(--surface-glass);
    backdrop-filter: blur(15px);
    font-weight: 500;
    font-size: 0.95rem;
    color: var(--text-color);
    font-family: 'Cairo', sans-serif;
    position: relative;
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.15), var(--shadow-md);
    background: var(--surface-color);
    transform: translateY(-2px) scale(1.01);
    outline: none;
}

.form-control:hover, .form-select:hover {
    border-color: var(--border-medium);
    box-shadow: var(--shadow-sm);
}

.form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-family: 'Cairo', sans-serif;
}

.form-label.required::after {
    content: '*';
    color: var(--danger-color);
    margin-right: var(--spacing-xs);
}

.form-text {
    color: var(--text-muted);
    font-size: 0.85rem;
    margin-top: var(--spacing-xs);
}

.input-group {
    position: relative;
    display: flex;
    align-items: stretch;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.input-group-text {
    background: var(--surface-secondary);
    border: 2px solid var(--border-color);
    border-left: none;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-muted);
    font-weight: 500;
}

/* Enhanced Alert Components */
.alert {
    border-radius: var(--border-radius-xl);
    margin: var(--spacing-xl) 0;
    padding: var(--spacing-lg) var(--spacing-xl);
    font-weight: 600;
    border: none;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(15px);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    animation: slideInDown 0.5s ease-out;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: currentColor;
    opacity: 0.8;
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
}

.alert::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, currentColor 0%, transparent 70%);
    opacity: 0.03;
    pointer-events: none;
}

.alert i {
    font-size: 1.25rem;
    opacity: 0.8;
}

.alert-success {
    background: var(--success-light);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.alert-danger {
    background: var(--danger-light);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.alert-info {
    background: var(--info-light);
    color: var(--info-color);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.alert-warning {
    background: var(--warning-light);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.alert-dismissible .btn-close {
    padding: var(--spacing-sm);
    margin: 0;
    background: none;
    border: none;
    opacity: 0.6;
    transition: var(--transition-fast);
}

.alert-dismissible .btn-close:hover {
    opacity: 1;
    transform: scale(1.1);
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Login Page */
.login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: var(--background-gradient);
    position: relative;
    padding: var(--spacing-xl);
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 25% 25%, rgba(79, 70, 229, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(124, 58, 237, 0.2) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagons" width="28" height="49" patternUnits="userSpaceOnUse" patternTransform="scale(0.5)"><polygon fill="%23ffffff" fill-opacity="0.05" points="14,1 28,8 28,22 14,29 0,22 0,8"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagons)"/></svg>') repeat;
    pointer-events: none;
    animation: backgroundFloat 15s ease-in-out infinite;
}

@keyframes backgroundFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.login-card {
    width: 100%;
    max-width: 500px;
    padding: 4rem;
    background: var(--surface-glass);
    backdrop-filter: blur(25px) saturate(180%);
    border-radius: var(--border-radius-2xl);
    box-shadow: var(--shadow-2xl);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-2xl) var(--border-radius-2xl) 0 0;
}

.login-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(79, 70, 229, 0.05) 0%, transparent 70%);
    pointer-events: none;
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.login-card .card-title {
    font-size: 2rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.login-card .card-title::before {
    content: '🔐';
    font-size: 1.5rem;
    filter: drop-shadow(0 2px 4px rgba(79, 70, 229, 0.3));
}

.login-card .form-control {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 1;
}

.login-card .form-control:focus {
    background: var(--surface-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.15), var(--shadow-lg);
}

.login-card .btn {
    position: relative;
    z-index: 1;
    font-size: 1.1rem;
    padding: var(--spacing-lg) var(--spacing-2xl);
}

/* Enhanced Layout Components */
.container {
    max-width: 1400px;
    padding: 0 var(--spacing-xl);
    margin: 0 auto;
}

.container-fluid {
    padding: 0 var(--spacing-lg);
}

body.modal-open {
    overflow: hidden;
    padding-left: 15px;
}

.page-header {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: var(--spacing-2xl);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    position: relative;
    font-family: 'Cairo', 'Amiri', serif;
    line-height: 1.2;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    animation: fadeInDown 1s ease-out;
}

.page-header::before {
    content: '📊';
    font-size: 2.5rem;
    filter: drop-shadow(0 4px 8px rgba(79, 70, 229, 0.3));
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.page-header::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-full);
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

/* Enhanced Spacing Utilities */
.mt-custom { margin-top: var(--spacing-2xl); }
.mb-custom { margin-bottom: var(--spacing-2xl); }
.pt-custom { padding-top: var(--spacing-2xl); }
.pb-custom { padding-bottom: var(--spacing-2xl); }

/* Enhanced List Components */
.list-group {
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    background: var(--surface-glass);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.list-group-item {
    background: transparent;
    border: none;
    border-bottom: 1px solid var(--border-light);
    padding: var(--spacing-lg);
    transition: var(--transition-fast);
    position: relative;
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass-gradient);
    opacity: 0;
    transition: var(--transition-fast);
    pointer-events: none;
}

.list-group-item:hover::before {
    opacity: 1;
}

.list-group-item:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-sm);
}

/* Enhanced Badge Components */
.badge {
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: 0.8rem;
    letter-spacing: 0.025em;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
}

.badge-primary {
    background: var(--primary-gradient);
    color: var(--text-inverse);
}

.badge-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: var(--text-inverse);
}

.badge-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    color: var(--text-inverse);
}

.badge-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
    color: var(--text-inverse);
}

.badge-info {
    background: linear-gradient(135deg, var(--info-color), #2563eb);
    color: var(--text-inverse);
}

/* Enhanced Modal Components */
.modal {
    backdrop-filter: blur(5px);
}

.modal-backdrop {
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(3px);
}

.modal-dialog {
    margin: var(--spacing-2xl) auto;
    max-width: 600px;
}

.modal-content {
    border: none;
    border-radius: var(--border-radius-2xl);
    box-shadow: var(--shadow-2xl);
    backdrop-filter: blur(25px) saturate(180%);
    background: var(--surface-glass);
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    border-bottom: 1px solid var(--border-light);
    border-radius: var(--border-radius-2xl) var(--border-radius-2xl) 0 0;
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.modal-header.bg-primary {
    background: var(--primary-gradient) !important;
    color: var(--text-inverse);
}

.modal-header.bg-success {
    background: linear-gradient(135deg, var(--success-color), #059669) !important;
    color: var(--text-inverse);
}

.modal-header.bg-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626) !important;
    color: var(--text-inverse);
}

.modal-header.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706) !important;
    color: var(--text-inverse);
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.modal-header:hover::before {
    left: 100%;
}

.modal-title {
    font-weight: 700;
    font-size: 1.5rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.modal-title i {
    font-size: 1.25rem;
    opacity: 0.9;
}

.modal-body {
    padding: var(--spacing-2xl);
    position: relative;
}

.modal-footer {
    border-top: 1px solid var(--border-light);
    padding: var(--spacing-xl);
    background: var(--surface-secondary);
    border-radius: 0 0 var(--border-radius-2xl) var(--border-radius-2xl);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-start;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    opacity: 0.6;
    transition: var(--transition-fast);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
}

.btn-close:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
}

.btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Enhanced Animations and Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.card, .alert, .table {
    animation: fadeInUp 0.8s ease-out;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

/* Loading States */
.loading {
    background: linear-gradient(90deg, var(--surface-secondary) 25%, var(--border-light) 50%, var(--surface-secondary) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.skeleton {
    background: var(--border-light);
    border-radius: var(--border-radius-md);
    animation: pulse 1.5s ease-in-out infinite;
}

/* Enhanced Scrollbar Styling */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: var(--surface-secondary);
    border-radius: var(--border-radius-md);
    margin: var(--spacing-xs);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: var(--border-radius-md);
    border: 2px solid var(--surface-secondary);
    transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
    box-shadow: var(--shadow-sm);
}

::-webkit-scrollbar-corner {
    background: var(--surface-secondary);
}

/* Enhanced Focus States */
*:focus {
    outline: none;
}

.focus-ring:focus {
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3);
    border-radius: var(--border-radius-md);
}

/* Print Styles */
@media print {
    body {
        background: white !important;
        color: black !important;
    }

    .navbar, .btn, .modal {
        display: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .page-header {
        font-size: 2rem;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .card-body {
        padding: var(--spacing-lg);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.9rem;
    }

    .table-responsive {
        font-size: 0.85rem;
    }

    .modal-dialog {
        margin: var(--spacing-md);
    }

    .login-card {
        padding: var(--spacing-2xl);
        margin: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .navbar {
        padding: var(--spacing-md);
    }

    .navbar-brand {
        font-size: 1.25rem;
    }

    .page-header {
        font-size: 1.75rem;
    }

    .card-header {
        padding: var(--spacing-lg);
    }

    .btn-group .btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }
}

/* Utility Classes */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: var(--surface-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.transition-all {
    transition: var(--transition-normal);
}
