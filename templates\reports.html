
{% extends 'base.html' %}

{% block title %}مركز التقارير{% endblock %}

{% block content %}
<h1 class="page-header"><i class="bi bi-file-earmark-bar-graph-fill me-2"></i>مركز التقارير</h1>

<div class="row">
    <!-- General Reports -->
    <div class="col-lg-6 col-md-12 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header"><h4><i class="bi bi-clipboard-data me-2"></i>تقارير عامة</h4></div>
            <div class="card-body d-flex flex-column">
                <p class="card-text">تقارير سريعة حول الوضع الحالي للمخزون.</p>
                <div class="mt-auto">
                    <a href="{{ url_for('inventory_report') }}" target="_blank" class="btn btn-primary">تقرير المخزون الحالي</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Report -->
    <div class="col-lg-6 col-md-12 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header"><h4><i class="bi bi-exclamation-triangle me-2"></i>تقرير المنتجات منخفضة المخزون</h4></div>
            <div class="card-body d-flex flex-column">
                <form action="{{ url_for('low_stock_report') }}" method="post" target="_blank">
                    <div class="mb-3">
                        <label for="threshold" class="form-label">الحد الأدنى للكمية</label>
                        <input type="number" id="threshold" class="form-control" name="threshold" placeholder="أدخل الحد الأدنى" required min="0">
                    </div>
                    <div class="mt-auto">
                        <button type="submit" class="btn btn-warning">عرض التقرير</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Product History Report -->
    <div class="col-lg-6 col-md-12 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header"><h4><i class="bi bi-hourglass-split me-2"></i>سجل حركة منتج</h4></div>
            <div class="card-body d-flex flex-column">
                <form action="{{ url_for('product_history_report') }}" method="post" target="_blank">
                    <div class="mb-3">
                        <label for="product_id" class="form-label">اختر المنتج</label>
                        <select name="product_id" id="product_id" class="form-select" required>
                            <option value="" selected disabled>-- اختر منتج --</option>
                            {% for product in products %}
                            <option value="{{ product.id }}">{{ product.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mt-auto">
                        <button type="submit" class="btn btn-info">عرض سجل المنتج</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Date Range Reports -->
    <div class="col-lg-6 col-md-12 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header"><h4><i class="bi bi-calendar-range me-2"></i>تقارير حسب الفترة</h4></div>
            <div class="card-body d-flex flex-column">
                <form action="{{ url_for('activity_log_report') }}" method="post" target="_blank">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date_activity" class="form-label">من تاريخ</label>
                            <input type="date" id="start_date_activity" name="start_date" class="form-control" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="end_date_activity" class="form-label">إلى تاريخ</label>
                            <input type="date" id="end_date_activity" name="end_date" class="form-control" required>
                        </div>
                    </div>
                    <div class="mt-auto d-grid gap-2">
                        <button type="submit" class="btn btn-primary">تقرير سجل النشاطات</button>
                        <button type="submit" formaction="{{ url_for('dispatch_summary_report') }}" class="btn btn-secondary">تقرير ملخص الصرف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
