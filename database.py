
import sqlite3
from werkzeug.security import generate_password_hash

# Connect to the database (it will be created if it doesn't exist)
conn = sqlite3.connect('c:/Users/<USER>/Desktop/mraqba/database.db')
cursor = conn.cursor()

# Create the admin table
cursor.execute("""
CREATE TABLE IF NOT EXISTS admin (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL
)
""")

# Create the products table
cursor.execute("""
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    quantity INTEGER NOT NULL
)
""")

# Create the activity_log table
cursor.execute("""
CREATE TABLE IF NOT EXISTS activity_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER,
    action TEXT NOT NULL,
    quantity_change INTEGER,
    representative_name TEXT,
    representative_rank TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    product_name_at_time TEXT,
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE SET NULL
)
""")

# Create the representatives table
cursor.execute("""
CREATE TABLE IF NOT EXISTS representatives (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    employee_id TEXT NOT NULL UNIQUE,
    rank TEXT NOT NULL
)
""")

# --- Schema Migration: Add notes column to activity_log ---
try:
    cursor.execute("ALTER TABLE activity_log ADD COLUMN notes TEXT")
    print("Column 'notes' added to 'activity_log' table.")
except sqlite3.OperationalError as e:
    if "duplicate column name" in str(e):
        print("Column 'notes' already exists in 'activity_log'.")
    else:
        raise

# --- Admin User Setup ---
# Check if an admin user already exists
cursor.execute("SELECT * FROM admin WHERE username = 'admin'")
if cursor.fetchone() is None:
    # Add a default admin user with a hashed password
    # The password is 'admin'
    hashed_password = generate_password_hash('admin', method='pbkdf2:sha256')
    cursor.execute("INSERT INTO admin (username, password) VALUES (?, ?)", ('admin', hashed_password))
    print("Default admin user ('admin'/'admin') created.")


# Commit the changes and close the connection
conn.commit()
conn.close()

print("Database and tables checked/updated successfully.")
