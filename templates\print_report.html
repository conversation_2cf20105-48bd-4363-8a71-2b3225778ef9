
<!doctype html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>تقرير صرف للمندوب: {{ representative.name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        @media print {
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="report-header text-center mb-4">
            <h2>كشف حساب مندوب</h2>
            <p>تاريخ الطباعة: {{ print_date }}</p>
        </div>

        <div class="card shadow-sm mb-4">
            <div class="card-header"><h4><i class="bi bi-person-badge me-2"></i>بيانات المندوب</h4></div>
            <div class="card-body">
                <p><strong>الاسم:</strong> {{ representative.name }}</p>
                <p><strong>رقم الموظف:</strong> {{ representative.employee_id }}</p>
                <p><strong>الرتبة:</strong> {{ representative.rank }}</p>
            </div>
        </div>

        <hr>

        <h4 class="mt-4">قائمة الأصناف المصروفة</h4>
        {% if logs %}
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>اسم المنتج</th>
                            <th>الكمية المصروفة</th>
                            <th>تاريخ الصرف</th>
                            <th>ملاحظات</th>
                            <th class="no-print">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ log.product_name_at_time }}</td>
                            <td>{{ -log.quantity_change }}</td>
                            <td>{{ log.timestamp.split(' ')[0] }}</td>
                            <td>{{ log.notes }}</td>
                            <td class="no-print">
                                <button class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editLogModal{{ log.id }}">تعديل</button>
                                <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteLogModal{{ log.id }}">حذف</button>
                            </td>
                        </tr>

                        <!-- Edit Log Modal -->
                        <div class="modal" id="editLogModal{{ log.id }}" tabindex="-1">
                            <div class="modal-dialog modal-dialog-centered">
                                <div class="modal-content">
                                    <div class="modal-header bg-warning text-dark">
                                        <h5 class="modal-title"><i class="bi bi-pencil-square me-2"></i>تعديل عملية الصرف</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <form action="{{ url_for('edit_dispatch_log', log_id=log.id) }}" method="post">
                                        <div class="modal-body">
                                            <p><strong>المنتج:</strong> {{ log.product_name_at_time }}</p>
                                            <input type="hidden" name="rep_id" value="{{ representative.id }}">
                                            <div class="mb-3">
                                                <label class="form-label">الكمية المصروفة الجديدة</label>
                                                <input type="number" class="form-control" name="new_quantity" value="{{ -log.quantity_change }}" required min="0">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">ملاحظات</label>
                                                <textarea class="form-control" name="notes" rows="3">{{ log.notes }}</textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                            <button type="submit" class="btn btn-warning">حفظ التغييرات</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Delete Log Modal -->
                        <div class="modal" id="deleteLogModal{{ log.id }}" tabindex="-1">
                            <div class="modal-dialog modal-dialog-centered">
                                <div class="modal-content">
                                    <div class="modal-header bg-danger text-white">
                                        <h5 class="modal-title"><i class="bi bi-trash-fill me-2"></i>تأكيد الحذف</h5>
                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                    </div>
                                    <form action="{{ url_for('delete_dispatch_log', log_id=log.id) }}" method="post">
                                        <div class="modal-body">
                                            <input type="hidden" name="rep_id" value="{{ representative.id }}">
                                            <p>هل أنت متأكد أنك تريد حذف عملية الصرف للمنتج '{{ log.product_name_at_time }}'؟</p>
                                            <p class="text-danger">سيتم إرجاع الكمية المصروفة ({{ -log.quantity_change }}) إلى المخزون.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info">لا توجد عمليات صرف مسجلة لهذا المندوب.</div>
        {% endif %}

        <div class="signature-section mt-5">
            <div class="text-center">
                <p><strong>توقيع أمين المستودع:</strong></p>
                <p>...................................</p>
            </div>
            <div class="text-center">
                <p><strong>توقيع المندوب المستلم:</strong></p>
                <p>...................................</p>
            </div>
        </div>

        <div class="text-center mt-5 no-print">
            <button onclick="window.print();" class="btn btn-primary"><i class="bi bi-printer-fill me-2"></i>طباعة التقرير</button>
            <button onclick="window.close();" class="btn btn-secondary">إغلاق</button>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
